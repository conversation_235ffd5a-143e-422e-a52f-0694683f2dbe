using System.Collections.Generic;
using UnityEngine;
using TMPro;

/// <summary>
/// Represents a player's physical play area in the 3D scene
/// </summary>
public class PlayerPlayArea : MonoBehaviour
{
    [HideInInspector] public GameObject earthReference;

    [Header("Player Reference")]
    [SerializeField] private int playerId;
    
    [<PERSON><PERSON>("Layout Settings")]
    [SerializeField] private Transform worldCardsParent;
    [SerializeField] private Transform technologyCardsParent;
    
    [<PERSON><PERSON>("Card Prefabs")]
    [SerializeField] private GameObject universalCardPrefab;
    
    [Header("Player Info TextMesh")]
    [SerializeField] public TextMeshPro moneyText;
    [SerializeField] public TextMeshPro scienceText;
    [SerializeField] public TextMeshPro playerNameText;
    [SerializeField] public TextMeshPro vpText;
    
    private Dictionary<string, GameObject> technologyCards = new Dictionary<string, GameObject>();
    

    // Dictionary mapping world GameObjects to their card instances in the play area
    private Dictionary<GameObject, GameObject> worldToCard = new Dictionary<GameObject, GameObject>();
    
    // Dictionary mapping world GameObjects to lists of module/ship cards at that location
    private Dictionary<GameObject, List<GameObject>> worldToModules = new Dictionary<GameObject, List<GameObject>>();

    // Reference to the player object
    public Player player;
    
    // World cards in order (for layout purposes)
    private List<GameObject> orderedWorldCards = new List<GameObject>();
    
    // Technology cards in order (for layout purposes)
    private List<GameObject> orderedTechCards = new List<GameObject>();

    private float worldCardSpacing = 4.9f;
    private float technologyCardSpacing = 3.5f;
    private float moduleCardSpacing = 3.5f;

    private void Awake()
    {

        // Set up player reference
        GameManager gameManager = GameManager.Instance;

        player = gameManager.Players[playerId];
        if (gameManager != null && playerId < gameManager.Players.Count)
        {
            player = gameManager.Players[playerId];

            // Set player name text
            if (playerNameText != null)
                playerNameText.text = $"Player {playerId + 1}";
        }
        else
        {
            Debug.LogWarning($"Could not find Player {playerId} in GameManager!");
        }
    }
    
    private void Start()
    {
        // Initialize player reference first
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null && playerId < gameManager.Players.Count)
        {
            player = gameManager.Players[playerId];
            
            // Set player name text
            if (playerNameText != null)
                playerNameText.text = $"Player {playerId + 1}";
        }
        else
        {
            Debug.LogError($"Could not find Player {playerId} in GameManager! Stopping initialization.");
            return;
        }

        // Initial setup - get Earth from WorldManager for consistency
        WorldManager worldManager = WorldManager.Instance;
        GameObject earth = worldManager.GetCelestialBodyByName("Earth");

        if (earth != null)
        {
            AddWorldCard(earth);
        }
        else
        {
            Debug.LogError("Could not find Earth GameObject for initial world card!");
        }
        
        // Update text displays
        UpdateInfoDisplay();
    }
    
    private void Update()
    {

    }
    
    // New method for adding starting ship
    public void AddStartingShip()
    {
        GameObject earth = GameObject.Find("Earth");
        if (earth != null && player != null)
        {
            Ship superheavy = new Ship
            {
                Name = "Super-heavy Reusable Rocket",
                CurrentLocation = earth,
                DeltaVPerFuel = 4,
                CargoCapacity = 4,
                Strength = 1
            };
            
            player.AddShip(superheavy);
            player.ShipsInPlay++;
            AddShipCard(earth, superheavy);
        }
    }
    
    // Check the implementation of UpdateInfoDisplay in PlayerPlayArea
    public void UpdateInfoDisplay()
    {
        if (player == null)
            return;

        // Directly update text fields here as a backup
        if (moneyText != null)
            moneyText.text = $"{player.Money}";

        if (scienceText != null) {
            scienceText.text = $"{player.ScienceValue}";
        }

        if (vpText != null)
            vpText.text = $"{player.VictoryPoints}";
            
        // Force text mesh updates
        if (scienceText != null) scienceText.ForceMeshUpdate();
        if (moneyText != null) moneyText.ForceMeshUpdate();
        if (vpText != null) vpText.ForceMeshUpdate();
    }

    /// <summary>
    /// Add the starting technology cards all players begin with
    /// </summary>
    public void AddStartingTechCards()
    {
        // These are the tier 0 starting tech cards all players have
        AddTechnologyCard("Solar Panel Module");
        AddTechnologyCard("ISRU Excavator Module");
        AddTechnologyCard("Super-heavy Reusable Rocket");
        AddTechnologyCard("Survey Probe");
        AddTechnologyCard("Assembler Module");
    }

    /// <summary>
    /// Add a world card to the player's play area
    /// </summary>
    public GameObject AddWorldCard(GameObject world)
    {

        // Check if we already have this world
        if (worldToCard.ContainsKey(world))
        {
            return worldToCard[world];
        }

        // Use WorldManager to create the appropriate world card
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager not found!");
            return null;
        }

        // Create the card instance using the appropriate template
        GameObject cardInstance = worldManager.CreateWorldCard(world, worldCardsParent);

        if (cardInstance == null)
        {
            Debug.LogError($"Failed to create world card for {world.name}!");
            return null;
        }

        // Add to dictionaries
        worldToCard[world] = cardInstance;
        worldToModules[world] = new List<GameObject>();

        // Insert the card in the correct position based on whether it's an orbit or celestial body
        InsertWorldCardInOrder(cardInstance, world);

        // Reposition all world cards
        RepositionWorldCards();
        return cardInstance;
    }

    /// <summary>
    /// Predefined order for world cards in the play area
    /// </summary>
    private readonly string[] worldCardOrder = new string[]
    {
        "LowSolarOrbit",
        "LowMercuryOrbit",
        "Mercury",
        "LowVenusOrbit",
        "Venus",
        "LowMoonOrbit",
        "Moon",
        "LowEarthOrbit",
        "Earth",
        "LowMarsOrbit",
        "Mars",
        "AsteroidTransfer",
        "AsteroidBelt",
        "LowCallistoOrbit",
        "Callisto",
        "LowGanymedeOrbit",
        "Ganymede",
        "LowEuropaOrbit",
        "Europa",
        "LowIoOrbit",
        "Io",
        "LowJupiterOrbit",
        "Jupiter",
        "LowTitanOrbit",
        "Titan",
        "LowEnceladusOrbit",
        "Enceladus",
        "LowSaturnOrbit",
        "Saturn",
        "LowUranusOrbit",
        "Uranus",
        "LowTritonOrbit",
        "Triton",
        "LowNeptuneOrbit",
        "Neptune",
        "SolarEscapeTrajectory"
    };

    /// <summary>
    /// Insert a world card in the correct position based on predefined order
    /// </summary>
    private void InsertWorldCardInOrder(GameObject cardInstance, GameObject world)
    {
        // Get the order index for this world
        int worldOrderIndex = GetWorldOrderIndex(world.name);

        // If world is not in our predefined order, add to the end
        if (worldOrderIndex == -1)
        {
            orderedWorldCards.Add(cardInstance);
            return;
        }

        // Find the correct position to insert based on order index
        for (int i = 0; i < orderedWorldCards.Count; i++)
        {
            GameObject existingWorld = GetWorldFromCard(orderedWorldCards[i]);
            if (existingWorld != null)
            {
                int existingOrderIndex = GetWorldOrderIndex(existingWorld.name);

                // If the existing world comes after this world in the order, insert here
                if (existingOrderIndex > worldOrderIndex || existingOrderIndex == -1)
                {
                    orderedWorldCards.Insert(i, cardInstance);
                    return;
                }
            }
        }

        // If we get here, this world should be at the end
        orderedWorldCards.Add(cardInstance);
    }

    /// <summary>
    /// Get the order index for a world name, returns -1 if not found
    /// </summary>
    private int GetWorldOrderIndex(string worldName)
    {
        for (int i = 0; i < worldCardOrder.Length; i++)
        {
            if (worldCardOrder[i].Equals(worldName, System.StringComparison.OrdinalIgnoreCase))
            {
                return i;
            }
        }
        return -1;
    }

    // Add this to PlayerPlayArea.cs
    public void SetScienceText(int scienceValue)
    {
        if (scienceText != null)
        {
            scienceText.text = $"{scienceValue}";
        }
        else
        {
            Debug.LogError("[PlayerPlayArea] scienceText field is null!");
        }
    }

    // Update AddTechnologyCard method to use universal prefab:
    public GameObject AddTechnologyCard(string technologyName)
    {
        // Check if we already have this technology
        if (technologyCards.ContainsKey(technologyName))
        {
            Debug.Log($"Player {playerId} already has {technologyName} technology card");
            return technologyCards[technologyName];
        }
        
        // Create the card instance using the universal prefab
        GameObject cardInstance = Instantiate(universalCardPrefab, technologyCardsParent);
        cardInstance.name = $"Tech_{technologyName}";
        
        // Configure the card based on technology name
        // This is where you'd set up the card's visual properties based on the technology data
        UniversalCardVisual3D cardVisual = cardInstance.GetComponent<UniversalCardVisual3D>();
        if (cardVisual != null)
        {
            // Create a CardData object with the technology information
            // You'd need to get this from your TechnologyDeckManager or similar
            CardData techData = GetTechnologyCardData(technologyName);
            if (techData != null)
            {
                cardVisual.SetupCard(techData);
                SetupCardInteractivity(cardInstance, techData);
            }
        }
        
        // Add to dictionary and ordered list
        technologyCards[technologyName] = cardInstance;
        orderedTechCards.Add(cardInstance);
        
        // Reposition all technology cards
        RepositionTechnologyCards();
        return cardInstance;
    }
    
    // Helper method to get technology card data by name
    private CardData GetTechnologyCardData(string technologyName)
    {
        
        // First, try to get the data from the TechnologyDeckManager
        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;
        if (deckManager != null)
        {
            // Get all parsed cards from the deck manager's card parser
            ExcelCardParser cardParser = deckManager.GetComponent<ExcelCardParser>();
            if (cardParser != null)
            {
                List<CardData> allCards = cardParser.ParseAllCards();
                
                // Search for the card with matching name
                foreach (CardData card in allCards)
                {
                    if (card.Name == technologyName)
                    {
                        return card;
                    }
                }
            }
        }
        
        // If not found in deck manager, try to get from player's owned technologies
        if (player != null)
        {
            List<TechnologyCardData> playerTechs = player.GetTechnologies();
            foreach (TechnologyCardData techData in playerTechs)
            {
                if (techData.cardName == technologyName)
                {
                    // Convert TechnologyCardData back to CardData
                    CardData cardData = new CardData();
                    cardData.Name = techData.cardName;
                    cardData.Type = "Technology";
                    cardData.Tier = techData.tier;
                    cardData.ScienceCost = techData.scienceValue;
                    cardData.BuildCost = new List<Resource>(techData.resourceCost);
                    cardData.Effect = techData.cardDescription;
                    
                    // For blueprint technologies, add additional data
                    if (techData is BlueprintTechnologyCardData blueprintData)
                    {
                        cardData.SubType = GetSubTypeFromBlueprintType(blueprintData.blueprintType);
                        cardData.PowerOutput = blueprintData.powerOutput;
                        cardData.BuildCost = new List<Resource>(blueprintData.buildCost);
                        
                        // Set effect parameters for ships
                        if (blueprintData.blueprintType == BlueprintType.Ship)
                        {
                            cardData.EffectParams = new Dictionary<string, object>();
                            cardData.EffectParams["deltaVPerFuel"] = blueprintData.deltaVPerFuel;
                            cardData.EffectParams["cargoCapacity"] = blueprintData.cargoCapacity;
                            cardData.EffectParams["shipStrength"] = blueprintData.shipStrength;
                            cardData.EffectParams["consumedOnSurvey"] = blueprintData.isConsumedOnSurvey;
                        }
                    }
                    
                    return cardData;
                }
            }
        }
        
        // If still not found, create from the starting tech cards that all players have
        CardData startingTechData = CreateStartingTechnologyData(technologyName);
        if (startingTechData != null)
        {

            return startingTechData;
        }
        
        // If all else fails, create a basic placeholder (this shouldn't happen in normal gameplay)
        Debug.LogWarning($"Could not find technology data for: {technologyName}. Creating placeholder");
        CardData placeholderData = new CardData();
        placeholderData.Name = technologyName;
        placeholderData.Type = "Technology";
        placeholderData.Tier = 0;
        placeholderData.Effect = "Technology effect";
        placeholderData.BuildCost = new List<Resource>();
        
        return placeholderData;
    }

    private void SetupCardInteractivity(GameObject cardObject, CardData cardData)
    {
        // Add action checker for glow effect
        CardActionChecker actionChecker = cardObject.GetComponent<CardActionChecker>();
        if (actionChecker == null)
        {
            actionChecker = cardObject.AddComponent<CardActionChecker>();
        }
        actionChecker.Initialize(cardData);
    }

    // Helper method to convert BlueprintType enum to SubType string
    private string GetSubTypeFromBlueprintType(BlueprintType blueprintType)
    {
        switch (blueprintType)
        {
            case BlueprintType.Ship:
                return "Ship";
            case BlueprintType.Power:
                return "Power Module";
            case BlueprintType.Extractor:
                return "Extractor Module";
            case BlueprintType.Processor:
                return "Processor Module";
            default:
                return "Module";
        }
    }

    // Helper method to create starting technology data
    private CardData CreateStartingTechnologyData(string technologyName)
    {
        CardData data = new CardData();
        data.Name = technologyName;
        data.Type = "Technology";
        data.Tier = 0;
        data.BuildCost = new List<Resource>();
        
        switch (technologyName)
        {
            case "Solar Panel Module":
                data.SubType = "Power Module";
                data.PowerOutput = 1;
                data.Effect = "+1 Power";
                data.BuildCost.Add(new Resource(ResourceType.Alloys, 1));
                data.BuildCost.Add(new Resource(ResourceType.Silicon, 1));
                break;
                
            case "ISRU Excavator Module":
                data.SubType = "Extractor Module";
                data.PowerRequired = 1;
                data.Effect = "Activate (1 Power): +1 each resource per deposit";
                data.BuildCost.Add(new Resource(ResourceType.Alloys, 1));
                break;
                
            case "Super-heavy Reusable Rocket":
                data.SubType = "Ship";
                data.Effect = "Cargo 2x2\n4 delta-v per fuel";
                data.BuildCost.Add(new Resource(ResourceType.Alloys, 1));
                data.BuildCost.Add(new Resource(ResourceType.Fuel, 1));
                data.EffectParams = new Dictionary<string, object>();
                data.EffectParams["deltaVPerFuel"] = 4f;
                data.EffectParams["cargoCapacity"] = 4;
                data.EffectParams["shipStrength"] = 1;
                break;
                
            case "Survey Probe":
                data.SubType = "Ship";
                data.Effect = "Cargo 1x1\n6 delta-v per fuel\nShip is consumed at destination for survey";
                data.BuildCost.Add(new Resource(ResourceType.Microchips, 1));
                data.EffectParams = new Dictionary<string, object>();
                data.EffectParams["deltaVPerFuel"] = 6f;
                data.EffectParams["cargoCapacity"] = 1;
                data.EffectParams["shipStrength"] = 0;
                data.EffectParams["consumedOnSurvey"] = true;
                break;
            case "Assembler Module":
                data.SubType = "Processor Module";
                data.Effect = "Activate (1 Power): Build a module";
                data.BuildCost.Add(new Resource(ResourceType.Alloys, 1));
                data.BuildCost.Add(new Resource(ResourceType.Microchips, 1));
                data.EffectParams = new Dictionary<string, object>();
                break;
            default:
                return null;
        }
        
        return data;
    }

     /// <summary>
    /// Add a module to a world in the player's play area
    /// </summary>
    public GameObject AddModuleCard(GameObject world, Module module)
    {
        // Check if we have this world
        if (!worldToCard.ContainsKey(world))
        {
            Debug.LogWarning($"Player {playerId} doesn't have a card for {world.name}! Adding it first.");
            AddWorldCard(world);
        }
        
        // Create module card using universal prefab
        GameObject cardInstance = Instantiate(universalCardPrefab, worldCardsParent);
        cardInstance.name = $"Module_{module.Name}";
        
        // Set up card visuals and data
        ModuleCardVisual cardVisual = cardInstance.GetComponent<ModuleCardVisual>();
        if (cardVisual != null)
        {
            cardVisual.SetupCard(module);
        }
        else
        {
            // If it's using UniversalCardVisual3D instead
            UniversalCardVisual3D universalVisual = cardInstance.GetComponent<UniversalCardVisual3D>();
            if (universalVisual != null)
            {
                // Create CardData from Module
                CardData moduleCardData = CreateCardDataFromModule(module);
                universalVisual.SetupCard(moduleCardData);
                SetupCardInteractivity(cardInstance, moduleCardData);
            }
        }
        
        // Add to the list of modules for this world
        worldToModules[world].Add(cardInstance);
        
        RepositionModuleCards(world);
        
        return cardInstance;
    }

    /// <summary>
    /// Add a ship to a world in the player's play area
    /// </summary>
    public GameObject AddShipCard(GameObject world, Ship ship)
    {
        // Check if we have this world
        if (!worldToCard.ContainsKey(world))
        {
            Debug.LogWarning($"Player {playerId} doesn't have a card for {world.name}! Adding it first.");
            AddWorldCard(world);
        }

        // Create ship card using universal prefab
        GameObject cardInstance = Instantiate(universalCardPrefab, worldCardsParent);
        cardInstance.name = $"Ship_{ship.Name}";

        // Set up card visuals and data
        ShipCardVisual cardVisual = cardInstance.GetComponent<ShipCardVisual>();
        if (cardVisual != null)
        {
            cardVisual.SetupCard(ship);
        }
        else
        {
            // If it's using UniversalCardVisual3D instead
            UniversalCardVisual3D universalVisual = cardInstance.GetComponent<UniversalCardVisual3D>();
            if (universalVisual != null)
            {
                // Create CardData from Ship
                CardData shipCardData = CreateCardDataFromShip(ship);
                universalVisual.SetupCard(shipCardData);
                SetupCardInteractivity(cardInstance, shipCardData);
            }
        }

        // Add to the list of modules for this world
        worldToModules[world].Add(cardInstance);

        RepositionModuleCards(world);

        return cardInstance;
    }


    // Helper method to create CardData from Module
    private CardData CreateCardDataFromModule(Module module)
    {
        CardData data = new CardData();
        data.Name = module.Name;
        data.Type = "ConstructedModule"; // Use different type to distinguish from buildable technology
        data.SubType = "Constructed" + GetSubTypeFromModuleType(module.Type); // Prefix with "Constructed" to distinguish
        data.PowerOutput = module.PowerOutput;
        data.PowerRequired = module.PowerRequired;
        data.Effect = module.ProcessorDescription ?? GetModuleDescription(module);
        data.IsWonder = module.IsWonder;
        data.BuildCost = new List<Resource>();
        data.Tier = module.Tier; // This was already here but Module.Tier isn't being set

        // Get tier from the original technology card data
        data.Tier = GetTierFromTechnologyName(module.Name);

        return data;
    }

    private int GetTierFromTechnologyName(string technologyName)
    {
        // First try to get from player's technologies
        if (player != null)
        {
            List<TechnologyCardData> playerTechs = player.GetTechnologies();
            foreach (TechnologyCardData techData in playerTechs)
            {
                if (techData.cardName == technologyName)
                {
                    return techData.tier;
                }
            }
        }

        // Try to get from deck manager's parsed cards
        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;
        if (deckManager != null)
        {
            ExcelCardParser cardParser = deckManager.GetComponent<ExcelCardParser>();
            if (cardParser != null)
            {
                List<CardData> allCards = cardParser.ParseAllCards();
                foreach (CardData card in allCards)
                {
                    if (card.Name == technologyName)
                    {
                        return card.Tier;
                    }
                }
            }
        }

        // Default to tier 1 for most modules, tier 0 for starting techs
        if (IsStartingTech(technologyName))
            return 0;

        return 1; // Default tier for most modules
    }

    private bool IsStartingTech(string technologyName)
    {
        return technologyName == "Solar Panel Module" ||
               technologyName == "ISRU Excavator Module" ||
               technologyName == "Super-heavy Reusable Rocket" ||
               technologyName == "Survey Probe" ||
               technologyName == "Assembler Module";
    }


    // Helper method to create CardData from Ship
    private CardData CreateCardDataFromShip(Ship ship)
    {
        CardData data = new CardData();
        data.Name = ship.Name;
        data.Type = "ConstructedShip"; // Use different type to distinguish from buildable technology
        data.SubType = "ConstructedShip"; // Use different subtype to distinguish from buildable technology
        data.Effect = $"Cargo {GetCargoSizeText(ship.CargoCapacity)}\n{ship.DeltaVPerFuel} delta-v per fuel";

        if (ship.Strength > 0)
        {
            data.Effect += $"\nStrength {ship.Strength}";
        }

        if (ship.IsConsumedOnSurvey)
        {
            data.Effect += "\nShip is consumed at destination for survey";
        }

        data.BuildCost = new List<Resource>();
        data.EffectParams = new Dictionary<string, object>();
        data.EffectParams["deltaVPerFuel"] = ship.DeltaVPerFuel;
        data.EffectParams["cargoCapacity"] = ship.CargoCapacity;
        data.EffectParams["shipStrength"] = ship.Strength;
        data.EffectParams["consumedOnSurvey"] = ship.IsConsumedOnSurvey;

        return data;
    }
    
    /// <summary>
    /// Get a text representation of cargo size (e.g., "2x4")
    /// </summary>
    private string GetCargoSizeText(int totalCapacity)
    {
        // Try to determine width and height from total capacity
        // This assumes cargo is always in a rectangular grid
        
        // Start with default values
        int cargoWidth = 2;
        int cargoHeight = totalCapacity / 2;
        
        // Some common cargo configurations
        if (totalCapacity == 1) { cargoWidth = 1; cargoHeight = 1; }      // 1x1
        else if (totalCapacity == 2) { cargoWidth = 1; cargoHeight = 2; } // 1x2
        else if (totalCapacity == 4) { cargoWidth = 2; cargoHeight = 2; } // 2x2
        else if (totalCapacity == 6) { cargoWidth = 2; cargoHeight = 3; } // 2x3
        else if (totalCapacity == 8) { cargoWidth = 2; cargoHeight = 4; } // 2x4
        else if (totalCapacity == 9) { cargoWidth = 3; cargoHeight = 3; } // 3x3
        else if (totalCapacity == 12) { cargoWidth = 3; cargoHeight = 4; } // 3x4
        else if (totalCapacity == 16) { cargoWidth = 4; cargoHeight = 4; } // 4x4
        else
        {
            // For other capacities, try to make a reasonable rectangle
            // Prefer wider than taller layouts
            if (totalCapacity <= 6)
            {
                cargoWidth = 2;
                cargoHeight = (totalCapacity + 1) / 2; // Round up division
            }
            else if (totalCapacity <= 12)
            {
                cargoWidth = 3;
                cargoHeight = (totalCapacity + 2) / 3; // Round up division
            }
            else
            {
                cargoWidth = 4;
                cargoHeight = (totalCapacity + 3) / 4; // Round up division
            }
        }
        
        return $"{cargoWidth}x{cargoHeight}";
    }

    /// <summary>
    /// Move a ship from one world to another
    /// </summary>
    public void MoveShipCard(GameObject shipCard, GameObject fromWorld, GameObject toWorld)
    {
        // Check if we have the source world
        if (!worldToModules.ContainsKey(fromWorld) || !worldToModules[fromWorld].Contains(shipCard))
        {
            Debug.LogWarning($"Ship {shipCard.name} not found at {fromWorld.name}!");
            return;
        }
        
        // Make sure we have the destination world
        if (!worldToCard.ContainsKey(toWorld))
        {
            AddWorldCard(toWorld);
        }
        
        // Move the ship card
        worldToModules[fromWorld].Remove(shipCard);
        worldToModules[toWorld].Add(shipCard);
        
        // Reposition cards at both locations
        RepositionModuleCards(fromWorld);
        RepositionModuleCards(toWorld);
        
        Debug.Log($"Player {playerId} moved {shipCard.name} from {fromWorld.name} to {toWorld.name}");
    }
    
    // Helper method to get subtype from ModuleType enum
    private string GetSubTypeFromModuleType(ModuleType moduleType)
    {
        switch (moduleType)
        {
            case ModuleType.Power:
                return "Power Module";
            case ModuleType.Extractor:
                return "Extractor Module";
            case ModuleType.Processor:
                return "Processor Module";
            case ModuleType.Wonder:
                return "Wonder";
            default:
                return "Module";
        }
    }

     // Helper method to get module description
    private string GetModuleDescription(Module module)
    {
        switch (module.Type)
        {
            case ModuleType.Power:
                return $"+{module.PowerOutput} Power";
            case ModuleType.Extractor:
                return "Activate: -1 Power, +1 each resource per deposit";
            case ModuleType.Processor:
                return $"Activate: -{module.PowerRequired} Power, process resources";
            case ModuleType.Wonder:
                return $"+{module.VictoryPointValue} VP" + (module.IsWonder ? " per turn" : "");
            default:
                return "";
        }
    }
    

    /// <summary>
    /// Remove a module or ship card from a world
    /// </summary>
    public void RemoveCard(GameObject world, GameObject card)
    {
        if (worldToModules.ContainsKey(world) && worldToModules[world].Contains(card))
        {
            worldToModules[world].Remove(card);
            Destroy(card);
            
            // Reposition remaining cards
            RepositionModuleCards(world);
        }
    }
    
    /// <summary>
    /// Remove a world card and all its modules/ships
    /// </summary>
    public void RemoveWorldCard(GameObject world)
    {
        if (worldToCard.ContainsKey(world))
        {
            // Destroy all module/ship cards at this world
            foreach (GameObject card in worldToModules[world])
            {
                Destroy(card);
            }
            
            // Remove from lists and dictionaries
            GameObject worldCard = worldToCard[world];
            orderedWorldCards.Remove(worldCard);
            worldToModules.Remove(world);
            worldToCard.Remove(world);
            
            // Destroy the world card
            Destroy(worldCard);
            
            // Reposition remaining world cards
            RepositionWorldCards();
            
            Debug.Log($"Player {playerId} removed {world.name} from play area");
        }
    }
    
    /// <summary>
    /// Remove a technology card
    /// </summary>
    public void RemoveTechnologyCard(string technologyName)
    {
        if (technologyCards.ContainsKey(technologyName))
        {
            GameObject card = technologyCards[technologyName];
            
            // Remove from lists and dictionaries
            orderedTechCards.Remove(card);
            technologyCards.Remove(technologyName);
            
            // Destroy the card
            Destroy(card);
            
            // Reposition remaining tech cards
            RepositionTechnologyCards();
        }
    }
    


    /// <summary>
    /// Reposition all world cards in a vertical column below Earth
    /// </summary>
    private void RepositionWorldCards()
    {
        float currentZPosition = 0f;

        for (int i = 0; i < orderedWorldCards.Count; i++)
        {
            GameObject worldCard = orderedWorldCards[i];
            GameObject world = GetWorldFromCard(worldCard);

            if (world == null) continue;

            // Position all cards (both celestial bodies and orbit locations) in the same way
            // All cards use the same Z position (0) and are positioned sequentially
            Vector3 position = new Vector3(0, -0.099f, currentZPosition);
            worldCard.transform.localPosition = position;
            currentZPosition += worldCardSpacing;

            // Reposition module cards for this world
            RepositionModuleCards(world);

            // Reposition resources for this world after the world card has been moved
            ResourceDisplayManager resourceDisplayManager = FindFirstObjectByType<ResourceDisplayManager>();
            if (resourceDisplayManager != null)
            {
                resourceDisplayManager.RepositionResourcesForLocation(playerId, world);
            }
        }
    }
    
    /// <summary>
    /// Reposition all technology cards in a row (in the opposite direction)
    /// </summary>
    private void RepositionTechnologyCards()
    {
        for (int i = 0; i < orderedTechCards.Count; i++)
        {
            // Modified to position cards in the opposite direction (right to left)
            // Calculate position so that newest card is on the left
            Vector3 position = new Vector3(-i * technologyCardSpacing, 0, 0);
            orderedTechCards[i].transform.localPosition = position;
        }
    }
    
    /// <summary>
    /// Reposition module/ship cards with proper offsets based on resource counts
    /// </summary>
    public void RepositionModuleCards(GameObject world)
    {
        if (!worldToCard.ContainsKey(world) || !worldToModules.ContainsKey(world))
        {
            Debug.LogWarning($"Cannot reposition modules: world {world?.name} not found in dictionaries");
            return;
        }

        GameObject worldCard = worldToCard[world];
        List<GameObject> moduleCards = worldToModules[world];

        // Default offset values
        float baseOffset = 3.5f;
        float resourceOffset = 0.0f;
        
        // Calculate resource offset based on actual resources at this location
        if (player != null)
        {
            // Get resource count at this location
            Dictionary<ResourceType, int> resources = player.GetResourcesOnPlanet(world);
            int physicalResourceCount = 0;

            foreach (var resource in resources)
            {
                if (IsPhysicalResource(resource.Key))
                {
                    // Only count physical resources with values > 0
                    // add a column for each 5 resources
                    if (resource.Value > 0)
                        physicalResourceCount++;
                    if (resource.Value > 5 )
                        physicalResourceCount++;
                    if (resource.Value > 10)
                        physicalResourceCount++;
                }
            }

            // Add offset based on resource count
            resourceOffset = physicalResourceCount * 1.05f;
        }
        
        // Total horizontal offset from world card
        float totalOffset = baseOffset + resourceOffset;
        
        // Position each module card
        for (int i = 0; i < moduleCards.Count; i++)
        {
            if (moduleCards[i] == null) continue;

            // Calculate position (modules positioned to the left of the world card)
            Vector3 worldCardPos = worldCard.transform.localPosition;

            // Calculate row and column
            int cardsPerColumn = 1;
            int column = i / cardsPerColumn;
            int row = i % cardsPerColumn;

            // Position relative to world card, after the resources
            Vector3 position = new Vector3(
                worldCardPos.x - totalOffset - (column * moduleCardSpacing),
                worldCardPos.y + 0.1f,  // Slightly raised
                worldCardPos.z + (row * moduleCardSpacing)  // Row offset
            );

            // Set position
            moduleCards[i].transform.localPosition = position;
        }
    }


    // Helper method to identify physical resources vs. abstract ones
    private bool IsPhysicalResource(ResourceType type)
    {
        return type != ResourceType.VP && 
            type != ResourceType.Science && 
            type != ResourceType.Dollars && 
            type != ResourceType.Power && 
            type != ResourceType.Strength && 
            type != ResourceType.Unknown;
    }
    
    /// <summary>
    /// Find the world GameObject for a world card
    /// </summary>
    private GameObject GetWorldFromCard(GameObject worldCard)
    {
        foreach (var kvp in worldToCard)
        {
            if (kvp.Value == worldCard)
                return kvp.Key;
        }
        return null;
    }
    
    /// <summary>
    /// Get all world cards
    /// </summary>
    public List<GameObject> GetWorldCards()
    {
        return orderedWorldCards;
    }
    
    /// <summary>
    /// Get all technology cards
    /// </summary>
    public List<GameObject> GetTechnologyCards()
    {
        return orderedTechCards;
    }
    
    /// <summary>
    /// Get all module/ship cards at a world
    /// </summary>
    public List<GameObject> GetModuleCardsAtWorld(GameObject world)
    {
        if (worldToModules.ContainsKey(world))
            return new List<GameObject>(worldToModules[world]);
            
        return new List<GameObject>();
    }
    
    /// <summary>
    /// Check if player has a specific technology card
    /// </summary>
    public bool HasTechnologyCard(string technologyName)
    {
        return technologyCards.ContainsKey(technologyName);
    }
    
    /// <summary>
    /// Get a specific technology card
    /// </summary>
    public GameObject GetTechnologyCard(string technologyName)
    {
        if (technologyCards.ContainsKey(technologyName))
            return technologyCards[technologyName];
            
        return null;
    }
    
    /// <summary>
    /// Get the world card GameObject for a specific location
    /// </summary>
    public GameObject GetWorldCard(GameObject location)
    {
        if (worldToCard.ContainsKey(location))
            return worldToCard[location];
            
        return null;
    }

    /// <summary>
    /// Highlight a world card (visual feedback)
    /// </summary>
    public void HighlightWorldCard(GameObject world)
    {
        if (worldToCard.ContainsKey(world))
        {
            GameObject worldCard = worldToCard[world];

            // Apply highlight effect (e.g., change scale or color)
            StartCoroutine(FlashCard(worldCard));
        }
    }
    
    /// <summary>
    /// Highlight a technology card (visual feedback)
    /// </summary>
    public void HighlightTechnologyCard(string technologyName)
    {
        if (technologyCards.ContainsKey(technologyName))
        {
            GameObject techCard = technologyCards[technologyName];
            
            // Apply highlight effect
            StartCoroutine(FlashCard(techCard));
        }
    }
    
    /// <summary>
    /// Flash effect for a card
    /// </summary>
    private System.Collections.IEnumerator FlashCard(GameObject card)
    {
        // Store original scale
        Vector3 originalScale = card.transform.localScale;
        
        // Flash 3 times
        for (int i = 0; i < 3; i++)
        {
            // Scale up
            card.transform.localScale = originalScale * 1.2f;
            yield return new WaitForSeconds(0.1f);
            
            // Scale back to normal
            card.transform.localScale = originalScale;
            yield return new WaitForSeconds(0.1f);
        }
    }
}